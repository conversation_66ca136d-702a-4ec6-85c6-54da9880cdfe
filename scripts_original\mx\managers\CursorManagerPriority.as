package mx.managers
{
   import mx.core.mx_internal;
   
   use namespace mx_internal;
   
   public final class CursorManagerPriority
   {
      
      mx_internal static const VERSION:String = "4.6.0.23201";
      
      public static const HIGH:int = 1;
      
      public static const MEDIUM:int = 2;
      
      public static const LOW:int = 3;
      
      public function CursorManagerPriority()
      {
         super();
      }
   }
}

